import React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { PhoneInput } from "@/components/form-inputs/phone-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { PasswordInput } from "@/components/form-inputs/password-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import { SwitchInput } from "@/components/form-inputs/switch-input";
import { FileInput } from "@/components/form-inputs/file-input";
import { User, MapPin, Briefcase, FileText, Key, Phone } from "lucide-react";
import {
  genderOptions,
  bloodGroups,
  maritalStatuses,
  occupations,
  incomeRanges,
  qualifications,
  titles,
} from "@/utils/form-options";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNavigate } from "react-router-dom";
import { useParent } from "@/context/parent-context";

export function ParentForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { addParent, editParent, isLoading } = useParent();

  const form = useForm({
    defaultValues: {
      // Personal Information
      title: initialData?.title || "",
      firstName: initialData?.firstName || "",
      lastName: initialData?.lastName || "",
      phone: initialData?.phone || "",
      gender: initialData?.gender || "",
      dateOfBirth: initialData?.dateOfBirth || "",
      bloodGroup: initialData?.bloodGroup || "",
      maritalStatus: initialData?.maritalStatus || "",
      nationality: initialData?.nationality || "Indian",
      religion: initialData?.religion || "",
      aadharNumber: initialData?.aadharNumber || "",
      panNumber: initialData?.panNumber || "",
      primaryParent: initialData?.primaryParent || true,

      // Address Information
      address: initialData?.address || "",
      city: initialData?.city || "",
      state: initialData?.state || "",
      pincode: initialData?.pincode || "",
      country: initialData?.country || "India",

      // Contact Information
      personalEmail: initialData?.personalEmail || "",
      alternatePhone: initialData?.alternatePhone || "",
      preferredContactMethod: initialData?.preferredContactMethod || "email",

      // Professional Information
      occupation: initialData?.occupation || "",
      companyName: initialData?.companyName || "",
      designation: initialData?.designation || "",
      workAddress: initialData?.workAddress || "",
      workPhone: initialData?.workPhone || "",
      qualification: initialData?.qualification || "",
      annualIncome: initialData?.annualIncome || "",

      // System Access & Permissions
      email: initialData?.email || "",
      password: initialData?.password || "",
      isActive: initialData?.isActive || true,

      // Additional Information
      bankName: initialData?.bankName || "",
      bankAccountNumber: initialData?.bankAccountNumber || "",
      ifscCode: initialData?.ifscCode || "",
      notes: initialData?.notes || "",

      // File uploads
      profilePhoto: initialData?.profilePhoto || null,
    },
  });

  const onSubmit = async (data) => {
    // Prevent double submission
    if (isLoading) return;

    try {
      if (editingId) {
        await editParent(editingId, data);
        toast.success("Parent information has been updated successfully.");
        navigate("/dashboard/parents");
      } else {
        await addParent(data);
        toast.success("New parent has been created successfully.");
        // Reset form after successful creation
        form.reset();
        navigate("/dashboard/parents");
      }
    } catch (error) {
      console.error("Error submitting parent form:", error);
      toast.error(
        error.message || "Failed to save parent information. Please try again."
      );
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <div className="space-y-6">
              <FormCard title="Personal Information" icon={User}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="title"
                        label="Title"
                        placeholder="Select title"
                        options={titles}
                        validation={{ required: "Title is required" }}
                      />
                      <div className="md:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <TextInput
                          form={form}
                          name="firstName"
                          label="First Name"
                          placeholder="Enter first name"
                          validation={{ required: "First name is required" }}
                        />
                        <TextInput
                          form={form}
                          name="lastName"
                          label="Last Name"
                          placeholder="Enter last name"
                          validation={{ required: "Last name is required" }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="gender"
                        label="Gender"
                        placeholder="Select gender"
                        options={genderOptions}
                        validation={{ required: "Gender is required" }}
                      />
                      <DateInput
                        form={form}
                        name="dateOfBirth"
                        label="Date of Birth"
                        placeholder="YYYY-MM-DD"
                        validation={{ required: "Date of birth is required" }}
                      />
                      <SelectInput
                        form={form}
                        name="bloodGroup"
                        label="Blood Group"
                        placeholder="Select blood group"
                        options={bloodGroups}
                      />
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="maritalStatus"
                        label="Marital Status"
                        placeholder="Select marital status"
                        options={maritalStatuses}
                      />
                      <TextInput
                        form={form}
                        name="nationality"
                        label="Nationality"
                        placeholder="Enter nationality"
                        validation={{ required: "Nationality is required" }}
                      />
                      <TextInput
                        form={form}
                        name="religion"
                        label="Religion"
                        placeholder="Enter religion"
                      />
                    </div>
                  </div>

                  <div className="">
                    <div className="space-y-4">
                      <TextInput
                        form={form}
                        name="aadharNumber"
                        label="Aadhar Number"
                        placeholder="Enter 12-digit Aadhar number"
                        validation={{
                          pattern: {
                            value: /^\d{12}$/,
                            message: "Aadhar number must be 12 digits",
                          },
                        }}
                      />
                      <TextInput
                        form={form}
                        name="panNumber"
                        label="PAN Number"
                        placeholder="Enter 10-digit PAN number"
                        validation={{
                          pattern: {
                            value: /^[A-Z]{5}\d{4}[A-Z]{1}$/,
                            message: "Invalid PAN number format",
                          },
                        }}
                      />
                      <SwitchInput
                        form={form}
                        name="primaryParent"
                        label="Primary Parent/Guardian"
                        description="This person is the primary contact for the student"
                      />
                    </div>
                  </div>

                  <FileInput
                    form={form}
                    name="profilePhoto"
                    label="Profile Photo"
                    description="Upload profile photo (PNG, JPG, JPEG) max size 5MB (Optional)"
                    accept="image/*"
                  />
                </div>
              </FormCard>

              <FormCard title="Contact Information" icon={Phone}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="personalEmail"
                    label="Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      required: "Email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PhoneInput
                    form={form}
                    name="phone"
                    label="Phone Number"
                    validation={{ required: "Phone number is required" }}
                  />
                  <PhoneInput
                    form={form}
                    name="alternatePhone"
                    label="Alternate Phone"
                  />
                  <SelectInput
                    form={form}
                    name="preferredContactMethod"
                    label="Preferred Contact Method"
                    placeholder="Select preferred contact method"
                    options={[
                      { label: "Email", value: "email" },
                      { label: "Phone", value: "phone" },
                    ]}
                  />
                </div>
              </FormCard>

              <FormCard title="Address Information" icon={MapPin}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="city"
                    label="City"
                    placeholder="Enter city"
                    validation={{ required: "City is required" }}
                  />
                  <TextInput
                    form={form}
                    name="state"
                    label="State"
                    placeholder="Enter state"
                    validation={{ required: "State is required" }}
                  />
                  <TextInput
                    form={form}
                    name="pincode"
                    label="Pincode"
                    placeholder="Enter pincode"
                    validation={{
                      required: "Pincode is required",
                      pattern: {
                        value: /^\d{6}$/,
                        message: "Pincode must be 6 digits",
                      },
                    }}
                  />
                  <TextInput
                    form={form}
                    name="country"
                    label="Country"
                    placeholder="Enter country"
                    validation={{ required: "Country is required" }}
                  />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="address"
                      label="Address"
                      placeholder="Enter current address"
                      validation={{ required: "Address is required" }}
                      inputProps={{ rows: 2 }}
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Professional Information" icon={Briefcase}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SelectInput
                    form={form}
                    name="occupation"
                    label="Occupation"
                    placeholder="Select occupation"
                    options={occupations}
                    validation={{ required: "Occupation is required" }}
                  />
                  <TextInput
                    form={form}
                    name="companyName"
                    label="Company/Organization Name"
                    placeholder="Enter company name"
                  />
                  <TextInput
                    form={form}
                    name="designation"
                    label="Designation"
                    placeholder="Enter designation"
                  />
                  <PhoneInput form={form} name="workPhone" label="Work Phone" />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="workAddress"
                      label="Work Address"
                      placeholder="Enter work address"
                      inputProps={{ rows: 2 }}
                    />
                  </div>
                  <SelectInput
                    form={form}
                    name="qualification"
                    label="Highest Qualification"
                    placeholder="Select qualification"
                    options={qualifications}
                  />
                  <SelectInput
                    form={form}
                    name="annualIncome"
                    label="Annual Income"
                    placeholder="Select income range"
                    options={incomeRanges}
                  />
                </div>
              </FormCard>

              <FormCard title="System Access & Permissions" icon={Key}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="email"
                    label="Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      required: "Email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PasswordInput
                    form={form}
                    name="password"
                    label="Password"
                    placeholder="Enter password for login"
                    validation={{
                      required: editingId ? false : "Password is required",
                      minLength: {
                        value: 8,
                        message: "Password must be at least 8 characters",
                      },
                    }}
                  />
                  <div className="md:col-span-2">
                    <CheckboxInput
                      form={form}
                      name="isActive"
                      label="Active Account"
                      description="Enable this to allow the user to log in."
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Additional Information" icon={FileText}>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <TextInput
                    form={form}
                    name="bankName"
                    label="Bank Name"
                    placeholder="Enter bank name"
                  />
                  <TextInput
                    form={form}
                    name="bankAccountNumber"
                    label="Bank Account Number"
                    placeholder="Enter bank account number"
                  />
                  <TextInput
                    form={form}
                    name="ifscCode"
                    label="IFSC Code"
                    placeholder="Enter IFSC code"
                    validation={{
                      pattern: {
                        value: /^[A-Z]{4}0[A-Z0-9]{6}$/,
                        message: "Invalid IFSC code format",
                      },
                    }}
                  />
                  <div className="lg:col-span-3">
                    <TextareaInput
                      form={form}
                      name="notes"
                      label="Additional Notes"
                      placeholder="Enter additional notes"
                      inputProps={{ rows: 3 }}
                    />
                  </div>
                </div>
              </FormCard>
            </div>
          </div>
          <FormFooter
            href="/parents"
            parent=""
            title="Parent"
            editingId={editingId}
          />
        </form>
      </Form>
    </div>
  );
}
