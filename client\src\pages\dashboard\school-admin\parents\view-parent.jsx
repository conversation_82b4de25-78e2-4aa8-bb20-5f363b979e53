import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Container } from "@/components/ui/container";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useParent } from "@/context/parent-context";
import { toast } from "sonner";
import {
  User,
  Phone,
  MapPin,
  Briefcase,
  FileText,
  Edit,
  ArrowLeft,
  CreditCard,
  Users,
} from "lucide-react";

const ViewParent = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchParentById } = useParent();
  const [parent, setParent] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadParent = async () => {
      try {
        const data = await fetchParentById(id);
        setParent(data);
      } catch (error) {
        console.error("Failed to fetch parent:", error);
        toast.error("Failed to load parent information");
        navigate("/dashboard/parents");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadParent();
    }
  }, [id, fetchParentById, navigate]);

  if (loading) {
    return (
      <Container className="py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <div className="h-64 bg-muted rounded"></div>
              <div className="h-48 bg-muted rounded"></div>
            </div>
            <div className="space-y-6">
              <div className="h-32 bg-muted rounded"></div>
              <div className="h-48 bg-muted rounded"></div>
            </div>
          </div>
        </div>
      </Container>
    );
  }

  if (!parent) {
    return (
      <Container className="py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold">Parent not found</h2>
          <p className="text-muted-foreground mt-2">
            The parent you're looking for doesn't exist.
          </p>
          <Button
            onClick={() => navigate("/dashboard/parents")}
            className="mt-4"
          >
            Back to Parents
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-8">
      <PageHeader
        title={`${parent.firstName} ${parent.lastName}`}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Parents", href: "/dashboard/parents" },
          { label: `${parent.firstName} ${parent.lastName}` },
        ]}
        actions={[
          {
            label: "Back",
            icon: ArrowLeft,
            href: "/dashboard/parents",
            variant: "outline",
          },
          {
            label: "Edit Parent",
            icon: Edit,
            href: `/dashboard/parents/${parent._id}/edit`,
          },
        ]}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Information */}
          <Card>
            <CardHeader className="flex flex-row items-center space-y-0 pb-4">
              <div className="flex items-center space-x-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={parent.profilePhoto} />
                  <AvatarFallback className="text-lg">
                    {parent.firstName?.charAt(0)}
                    {parent.lastName?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Personal Information
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge
                      variant={
                        parent.status === "active" ? "success" : "destructive"
                      }
                    >
                      {parent.status}
                    </Badge>
                    {parent.primaryParent && (
                      <Badge variant="outline">Primary Parent</Badge>
                    )}
                    <Badge variant={parent.isActive ? "success" : "secondary"}>
                      Account: {parent.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Full Name
                  </label>
                  <p className="text-sm">
                    {parent.title} {parent.firstName} {parent.lastName}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Gender
                  </label>
                  <p className="text-sm">{parent.gender}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Date of Birth
                  </label>
                  <p className="text-sm">
                    {parent.dateOfBirth
                      ? new Date(parent.dateOfBirth).toLocaleDateString()
                      : "N/A"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Blood Group
                  </label>
                  <p className="text-sm">{parent.bloodGroup || "N/A"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Marital Status
                  </label>
                  <p className="text-sm">{parent.maritalStatus || "N/A"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Nationality
                  </label>
                  <p className="text-sm">{parent.nationality}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Religion
                  </label>
                  <p className="text-sm">{parent.religion || "N/A"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Aadhar Number
                  </label>
                  <p className="text-sm">{parent.aadharNumber || "N/A"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    PAN Number
                  </label>
                  <p className="text-sm">{parent.panNumber || "N/A"}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Personal Email
                  </label>
                  <p className="text-sm">{parent.personalEmail}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    System Email
                  </label>
                  <p className="text-sm">{parent.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Phone Number
                  </label>
                  <p className="text-sm">{parent.phone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Alternate Phone
                  </label>
                  <p className="text-sm">{parent.alternatePhone || "N/A"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Preferred Contact
                  </label>
                  <p className="text-sm capitalize">
                    {parent.preferredContactMethod}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Address Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Address
                </label>
                <p className="text-sm">{parent.address}</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    City
                  </label>
                  <p className="text-sm">{parent.city}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    State
                  </label>
                  <p className="text-sm">{parent.state}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Pincode
                  </label>
                  <p className="text-sm">{parent.pincode}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Country
                  </label>
                  <p className="text-sm">{parent.country}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Quick Info
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Children</span>
                <span className="text-sm font-medium">
                  {parent.children?.length || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">
                  Member Since
                </span>
                <span className="text-sm font-medium">
                  {new Date(parent.createdAt).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">
                  Last Updated
                </span>
                <span className="text-sm font-medium">
                  {new Date(parent.updatedAt).toLocaleDateString()}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Professional Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Professional Info
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Occupation
                </label>
                <p className="text-sm">{parent.occupation}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Company
                </label>
                <p className="text-sm">{parent.companyName || "N/A"}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Designation
                </label>
                <p className="text-sm">{parent.designation || "N/A"}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Qualification
                </label>
                <p className="text-sm">{parent.qualification || "N/A"}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Annual Income
                </label>
                <p className="text-sm">{parent.annualIncome || "N/A"}</p>
              </div>
              {parent.workPhone && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Work Phone
                  </label>
                  <p className="text-sm">{parent.workPhone}</p>
                </div>
              )}
              {parent.workAddress && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Work Address
                  </label>
                  <p className="text-sm">{parent.workAddress}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Banking Information */}
          {(parent.bankName || parent.bankAccountNumber || parent.ifscCode) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Banking Info
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {parent.bankName && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Bank Name
                    </label>
                    <p className="text-sm">{parent.bankName}</p>
                  </div>
                )}
                {parent.bankAccountNumber && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Account Number
                    </label>
                    <p className="text-sm">{parent.bankAccountNumber}</p>
                  </div>
                )}
                {parent.ifscCode && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      IFSC Code
                    </label>
                    <p className="text-sm">{parent.ifscCode}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Additional Notes */}
          {parent.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Additional Notes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{parent.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </Container>
  );
};

export default ViewParent;
