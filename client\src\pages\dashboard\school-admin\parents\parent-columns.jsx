import React, { useState } from "react";
import { StatusColumn } from "@/components/data-table/data-table-columns/status-column";
import { ImageColumn } from "@/components/data-table/data-table-columns/image-column";
import { DateColumn } from "@/components/data-table/data-table-columns/data-columns";
import { SortableColumn } from "@/components/data-table/data-table-columns/sortable-column";
import { ActionColumn } from "@/components/data-table/data-table-columns/action-column";
import { useNavigate } from "react-router-dom";
import { useParent } from "@/context/parent-context";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

export const ParentColumns = () => {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <SortableColumn column={column} title="Parent Name" />
      ),
      cell: ({ row }) => (
        <div className="flex items-center space-x-3">
          <ImageColumn
            src={row.original.profilePhoto}
            alt={`${row.original.firstName} ${row.original.lastName}`}
            fallbackText={`${row.original.firstName?.charAt(0) || ""}${
              row.original.lastName?.charAt(0) || ""
            }`}
          />
          <div className="flex flex-col">
            <div className="font-medium">
              {row.original.firstName} {row.original.lastName}
            </div>
            <div className="text-sm text-muted-foreground">
              {row.original.personalEmail || row.original.email}
            </div>
            {row.original.primaryParent && (
              <div className="text-xs text-primary font-medium">
                Primary Parent
              </div>
            )}
          </div>
        </div>
      ),
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "phone",
      header: ({ column }) => (
        <SortableColumn column={column} title="Contact" />
      ),
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="text-muted-foreground">{row.original.phone}</div>
          {row.original.alternatePhone && (
            <div className="text-xs text-muted-foreground">
              Alt: {row.original.alternatePhone}
            </div>
          )}
          <div className="text-xs text-primary">
            {row.original.preferredContactMethod === "email" ? "📧" : "📞"}{" "}
            Preferred
          </div>
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "gender",
      header: ({ column }) => <SortableColumn column={column} title="Gender" />,
      cell: ({ row }) => (
        <div className="text-sm text-center">{row.original.gender}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "city",
      header: ({ column }) => <SortableColumn column={column} title="City" />,
      cell: ({ row }) => (
        <div className="text-sm">
          {row.original.city}, {row.original.state}
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "occupation",
      header: ({ column }) => (
        <SortableColumn column={column} title="Occupation" />
      ),
      cell: ({ row }) => (
        <div className="text-sm text-center">{row.original.occupation}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <div className="text-sm">
          <StatusColumn
            row={row}
            statusField="status"
            variant={{
              active: "success",
              inactive: "destructive",
            }}
          />
          <div className="text-xs text-muted-foreground mt-1">
            Account: {row.original.isActive ? "Active" : "Inactive"}
          </div>
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "createdAt",
      header: "Date Added",
      cell: ({ row }) => <DateColumn row={row} accessorKey="createdAt" />,
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      id: "actions",
      cell: ({ row }) => <ParentActions row={row} />,
    },
  ];
};

const ParentActions = ({ row }) => {
  const navigate = useNavigate();
  const { removeParent } = useParent();
  const [open, setOpen] = useState(false);

  const handleView = () => {
    navigate(`/dashboard/parents/${row.original._id}`);
  };

  const handleEdit = () => {
    navigate(`/dashboard/parents/${row.original._id}/edit`);
  };

  const handleDelete = async () => {
    try {
      await removeParent(row.original._id);
      toast.success(
        `${row.original.firstName} ${row.original.lastName} deleted successfully.`
      );
      setOpen(false);
    } catch (error) {
      console.error("Failed to delete parent:", error);
      toast.error("Failed to delete parent. Please try again.");
    }
  };

  return (
    <>
      <ActionColumn
        row={row}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={() => setOpen(true)}
      />
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogTrigger asChild />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Delete {row.original.firstName} {row.original.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this parent? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
