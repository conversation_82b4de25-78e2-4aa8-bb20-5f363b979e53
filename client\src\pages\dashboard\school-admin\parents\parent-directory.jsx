import { useEffect } from "react";
import { useParent } from "@/context/parent-context";
import {
  <PERSON>,
  <PERSON>r<PERSON><PERSON><PERSON>,
  PlusCircle,
  CalendarClock,
  Landmark,
} from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { ParentColumns } from "@/pages/dashboard/school-admin/parents/parent-columns";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";

const ParentDirectory = () => {
  const { parents, isLoading, fetchAllParents } = useParent();

  useEffect(() => {
    fetchAllParents();
  }, []);

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Parent Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Parents" },
          ]}
          actions={[
            {
              label: "New Parent",
              icon: PlusCircle,
              href: "/dashboard/parents/create",
            },
          ]}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <StatCard
            title="Total Parents"
            value={parents.length}
            description="All registered parents"
            icon={Users}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Active Parents"
            value={parents.filter((p) => p.status === "active").length}
            description="Currently active"
            icon={UserCheck}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Primary Parents"
            value={parents.filter((p) => p.primaryParent).length}
            description="Primary guardians"
            icon={Users}
            isLoading={isLoading}
          />

          <StatCard
            title="Recent Registrations"
            value={
              parents.filter((parent) => {
                const createdAt = new Date(parent.createdAt);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return createdAt >= thirtyDaysAgo;
              }).length
            }
            description="Joined in last 30 days"
            icon={CalendarClock}
            isLoading={isLoading}
          />

          <StatCard
            title="Top City"
            value={(() => {
              if (parents.length === 0) return "N/A";
              const cityCount = parents.reduce((acc, parent) => {
                const city = parent.city || "Unknown";
                acc[city] = (acc[city] || 0) + 1;
                return acc;
              }, {});
              let topCity = "Unknown";
              let max = 0;
              for (const [city, count] of Object.entries(cityCount)) {
                if (count > max) {
                  topCity = city;
                  max = count;
                }
              }
              return topCity;
            })()}
            description="Most parents from"
            icon={Landmark}
            isLoading={isLoading}
          />
        </div>

        <div>
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={parents}
              columns={ParentColumns()}
              model="parent"
            />
          )}
        </div>
      </main>
    </div>
  );
};

export default ParentDirectory;
